name: Build Site, App & Runtime

on:
  workflow_dispatch:
    inputs:
      build_electron:
        description: 'Build Electron App'
        required: false
        default: false
        type: boolean
  workflow_call:
    outputs:
      version:
        description: "Version of the built application"
        value: ${{ jobs.build-app.outputs.version }}
      python-changed:
        description: "Whether Python files changed"
        value: ${{ jobs.build-app.outputs.python-changed }}
      main-whispr-changed:
        description: "Whether main.py or whispr folder changed"
        value: ${{ jobs.build-app.outputs.main-whispr-changed }}
      deps-changed:
        description: "Whether dependencies changed"
        value: ${{ jobs.build-app.outputs.deps-changed }}

jobs:
  build-site:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'one.whispr-site/package-lock.json'

      - name: Install dependencies
        run: |
          cd one.whispr-site
          # Clean install to ensure platform-specific native binaries are correct
          npm ci --prefer-offline --no-audit

      - name: Apply database migrations
        run: |
          cd one.whispr-site
          npx tsx scripts/apply-migrations.ts
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: Build
        run: |
          cd one.whispr-site
          npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_SITE_URL: ${{ secrets.NEXT_PUBLIC_SITE_URL }}

      - name: Upload site build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: onewhispr-site
          path: |
            one.whispr-site/.next/**/*
            one.whispr-site/package.json
            one.whispr-site/package-lock.json
            one.whispr-site/public/**/*
            one.whispr-site/next.config.ts
            one.whispr-site/scripts/**/*
            one.whispr-site/src/migrations/**/*

  build-app:
    runs-on: windows-latest
    if: github.event.inputs.build_electron == 'true' || contains(github.event.head_commit.message, '[build-electron]')
    outputs:
      version: ${{ steps.get-version.outputs.version }}
      python-changed: ${{ steps.python-changes.outputs.python-changed }}
      main-whispr-changed: ${{ steps.python-changes.outputs.main-whispr-changed }}
      deps-changed: ${{ steps.python-changes.outputs.deps-changed }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for git diff

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies for main app
        run: |
          cd one.whispr-app
          npm ci

      - name: Check for Python file changes
        id: python-changes
        run: |
          # Check for different types of Python changes
          $MAIN_WHISPR_CHANGED = $false
          $DEPS_CHANGED = $false

          # Check if main.py or whispr folder changed (quick update)
          $mainWhisprFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/main\.py$|^one\.whispr-app/python/whispr/'
          if ($mainWhisprFiles) {
            $MAIN_WHISPR_CHANGED = $true
            "main-whispr-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Main.py or whispr folder changed - quick update needed"
          } else {
            "main-whispr-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

          # Check if dependencies or spec files changed (full rebuild)
          $depsFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/.*\.spec$|^one\.whispr-app/python/utils/python-setup\.ts$|^one\.whispr-app/python/requirements\.txt$|^one\.whispr-app/package\.json$'
          if ($depsFiles) {
            $DEPS_CHANGED = $true
            "deps-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Dependencies or build configuration changed - full rebuild needed"
          } else {
            "deps-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          }

          # Set overall python-changed flag
          if ($MAIN_WHISPR_CHANGED -or $DEPS_CHANGED) {
            "python-changed=true" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "Python files have changed, will rebuild"
          } else {
            "python-changed=false" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
            Write-Host "No Python files changed, skipping Python build"
          }
        shell: powershell

      - name: Setup Python environment
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          cd one.whispr-app
          # Setup Python environment and dependencies (voice models downloaded on-demand)
          npm run backend-setup

      - name: Install dependencies
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          cd one.whispr-app
          npm install
        shell: powershell

      - name: Compile Python backend (Full rebuild)
        if: steps.python-changes.outputs.deps-changed == 'true'
        run: |
          cd one.whispr-app
          # This will download Whisper base model (~277MB) and include it in base runtime
          npm run backend-compile

      - name: Quick update (bytecode compilation only)
        if: steps.python-changes.outputs.main-whispr-changed == 'true' && steps.python-changes.outputs.deps-changed == 'false'
        run: |
          cd one.whispr-app
          npm run backend-compile:quick

      - name: Build main Electron app to individual files
        run: |
          cd one.whispr-app
          npm run build:files

      - name: Install dependencies for setup
        run: |
          cd one.whispr-setup
          npm ci

      - name: Remove Scripts.7z from main app (not needed in Microsoft Store)
        run: |
          cd one.whispr-app
          Remove-Item ".release/win-unpacked/resources/backend/OneWhispr-Scripts.7z" -Force -ErrorAction SilentlyContinue
        shell: powershell

      - name: Build setup/installer (Microsoft Store)
        run: |
          cd one.whispr-setup
          npm run build:files:microsoft
        env:
          IS_MICROSOFT: true
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Remove remaining 7z files from main app for direct distribution
        run: |
          cd one.whispr-app
          Remove-Item ".release/win-unpacked/resources/backend/OneWhispr-Runtime-Base.7z" -Force -ErrorAction SilentlyContinue
          Remove-Item ".release/win-unpacked/resources/backend/msstore.json" -Force -ErrorAction SilentlyContinue
        shell: powershell

      - name: Build setup/installer (Direct distribution)
        run: |
          cd one.whispr-setup
          npm run build:files:direct
        env:
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Debug build outputs
        run: |
          echo "=== Microsoft Store build output ==="
          find one.whispr-setup/.release-microsoft -type f 2>/dev/null || echo "Microsoft Store directory not found"
          echo "=== Direct build output ==="
          find one.whispr-setup/.release-direct -type f 2>/dev/null || echo "Direct directory not found"
          echo "=== Main app output ==="
          find one.whispr-app/.release -type f | head -10 2>/dev/null || echo "Main app directory not found"
          echo "=== Looking for .appx files anywhere ==="
          find . -name "*.appx" -type f 2>/dev/null || echo "No .appx files found"
        shell: bash

      - name: Get version for outputs
        id: get-version
        run: |
          cd one.whispr-setup
          $VERSION = node -p "require('./package.json').version"
          "version=$VERSION" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          Write-Host "Version: $VERSION"
        shell: powershell

      - name: Upload Direct Distribution Package
        uses: actions/upload-artifact@v4
        with:
          name: onewhispr-setup-exe
          path: one.whispr-setup/.release-direct/squirrel-windows/*
          if-no-files-found: error

      # Microsoft Store build will be uploaded to Microsoft Store later

      - name: Upload Unpacked Main App
        uses: actions/upload-artifact@v4
        with:
          name: onewhispr-main-app
          path: one.whispr-app/.release/win-unpacked/**/*
          if-no-files-found: error

      - name: Upload Python Build Artifacts
        uses: actions/upload-artifact@v4
        if: steps.python-changes.outputs.python-changed == 'true'
        with:
          name: onewhispr-python-builds
          path: |
            one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z
            one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z
