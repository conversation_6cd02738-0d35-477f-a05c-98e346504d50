name: Deploy to VPS

on:
  workflow_dispatch:
    inputs:
      use_latest_artifacts:
        description: 'Use latest artifacts from previous build (skip building)'
        required: false
        default: false
        type: boolean
      force_python_deploy:
        description: 'Force deploy Python artifacts (runtime + scripts)'
        required: false
        default: false
        type: boolean
      force_scripts_only:
        description: 'Force deploy scripts only (quick update)'
        required: false
        default: false
        type: boolean
      rollback_to_version:
        description: 'Rollback to specific version (e.g., 1.2.1)'
        required: false
        type: string
      emergency_rollback:
        description: 'Emergency rollback to previous version'
        required: false
        default: false
        type: boolean
  workflow_call:
    inputs:
      version:
        description: 'Version from build workflow'
        required: false
        type: string
      python-changed:
        description: 'Whether Python files changed'
        required: false
        type: string
      main-whispr-changed:
        description: 'Whether main.py or whispr folder changed'
        required: false
        type: string
      deps-changed:
        description: 'Whether dependencies changed'
        required: false
        type: string
      use_latest_artifacts:
        description: 'Use latest artifacts instead of current workflow'
        required: false
        type: boolean
        default: false
      force_python_deploy:
        description: 'Force deploy Python artifacts'
        required: false
        type: boolean
        default: false
      force_scripts_only:
        description: 'Force deploy scripts only'
        required: false
        type: boolean
        default: false
      rollback_to_version:
        description: 'Rollback to specific version'
        required: false
        type: string
      emergency_rollback:
        description: 'Emergency rollback'
        required: false
        type: boolean
        default: false

jobs:
  deploy-site:
    runs-on: ubuntu-latest
    if: inputs.rollback_to_version == '' && inputs.emergency_rollback != true
    
    steps:
      - name: Checkout code (for utility scripts)
        uses: actions/checkout@v4

      - name: Download site artifacts (if available)
        uses: actions/download-artifact@v4
        if: inputs.use_latest_artifacts != true
        continue-on-error: true
        with:
          name: onewhispr-site
          path: one.whispr-site/

      - name: Download latest site artifacts
        uses: dawidd6/action-download-artifact@v3
        if: inputs.use_latest_artifacts == true
        with:
          workflow: build.yaml
          workflow_conclusion: success
          name: onewhispr-site
          path: one.whispr-site/
          search_artifacts: true
          if_no_artifact_found: ignore

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Deploy site to VPS
        if: hashFiles('one.whispr-site/.next/**') != ''
        run: |
          rsync -avz --delete one.whispr-site/.next one.whispr-site/package.json one.whispr-site/package-lock.json one.whispr-site/public one.whispr-site/next.config.ts one.whispr-site/scripts one.whispr-site/src/migrations ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.DEPLOY_PATH }}
          
      - name: Deploy utility scripts to VPS
        run: |
          # Deploy rollback script and make it executable
          scp .github/scripts/rollback.sh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.DEPLOY_PATH }}/scripts/
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "chmod +x ${{ secrets.DEPLOY_PATH }}/scripts/rollback.sh"
          
      - name: Restart application
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd ${{ secrets.DEPLOY_PATH }}
            echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}" > .env.production
            echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> .env.production
            echo "NEXT_PUBLIC_SITE_URL=${{ secrets.NEXT_PUBLIC_SITE_URL }}" >> .env.production
            echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> .env.production
            npm install --production
            
            # Run migrations using our improved script
            npx tsx scripts/apply-migrations.ts
            
            pm2 restart one-whispr-site || pm2 start npm --name "one-whispr-site" -- start 

  deploy-app:
    runs-on: ubuntu-latest
    if: inputs.rollback_to_version == '' && inputs.emergency_rollback != true
    
    steps:
      - name: Download Squirrel Windows installer
        uses: actions/download-artifact@v4
        if: inputs.use_latest_artifacts != true
        with:
          name: onewhispr-setup-exe
          path: artifacts/one.whispr-setup/.release-direct/

      - name: Download Main App files
        uses: actions/download-artifact@v4
        if: inputs.use_latest_artifacts != true
        with:
          name: onewhispr-main-app
          path: artifacts/one.whispr-app/.release/win-unpacked/

      - name: Download Python build artifacts (from current workflow)
        uses: actions/download-artifact@v4
        if: inputs.use_latest_artifacts != true
        continue-on-error: true
        with:
          name: onewhispr-python-builds
          path: artifacts/python-builds/

      - name: Download latest Squirrel Windows installer
        uses: dawidd6/action-download-artifact@v3
        if: inputs.use_latest_artifacts == true
        with:
          workflow: build.yaml
          workflow_conclusion: success
          name: onewhispr-setup-exe
          path: artifacts/one.whispr-setup/.release-direct/
          search_artifacts: true

      - name: Download latest Main App files
        uses: dawidd6/action-download-artifact@v3
        if: inputs.use_latest_artifacts == true
        with:
          workflow: build.yaml
          workflow_conclusion: success
          name: onewhispr-main-app
          path: artifacts/one.whispr-app/.release/win-unpacked/
          search_artifacts: true

      - name: Download latest Python artifacts
        uses: dawidd6/action-download-artifact@v3
        if: inputs.use_latest_artifacts == true
        with:
          workflow: build.yaml
          workflow_conclusion: success
          name: onewhispr-python-builds
          path: artifacts/python-builds/
          search_artifacts: true
          if_no_artifact_found: ignore

      - name: Get deployment parameters
        id: get-params
        run: |
          # Determine version and change flags
          if [ "${{ inputs.use_latest_artifacts }}" = "true" ]; then
            # Use timestamp for deploy-only
            VERSION=$(date +%Y.%m.%d.%H%M)
            PYTHON_CHANGED="${{ inputs.force_python_deploy }}"
            MAIN_WHISPR_CHANGED="${{ inputs.force_scripts_only }}"
            DEPS_CHANGED="${{ inputs.force_python_deploy }}"
          else
            # Use values from workflow_call inputs
            VERSION="${{ inputs.version }}"
            PYTHON_CHANGED="${{ inputs.python-changed }}"
            MAIN_WHISPR_CHANGED="${{ inputs.main-whispr-changed }}"
            DEPS_CHANGED="${{ inputs.deps-changed }}"
          fi
          
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "python-changed=$PYTHON_CHANGED" >> $GITHUB_OUTPUT
          echo "main-whispr-changed=$MAIN_WHISPR_CHANGED" >> $GITHUB_OUTPUT
          echo "deps-changed=$DEPS_CHANGED" >> $GITHUB_OUTPUT
          echo "Deploying version: $VERSION"

      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Debug artifacts structure
        run: |
          echo "=== Artifacts directory structure ==="
          echo "Direct distribution files:"
          ls -la one.whispr-setup/.release-direct/squirrel-windows/ 2>/dev/null || echo "No direct distribution files found"
          echo "Main app files:"
          ls -la one.whispr-app/.release/win-unpacked/ 2>/dev/null || echo "No main app files found"
          echo "=== End artifacts structure ==="
        shell: bash

      - name: Prepare deployment structure
        run: |
          VERSION="${{ steps.get-params.outputs.version }}"
          PYTHON_CHANGED="${{ steps.get-params.outputs.python-changed }}"
          MAIN_WHISPR_CHANGED="${{ steps.get-params.outputs.main-whispr-changed }}"
          DEPS_CHANGED="${{ steps.get-params.outputs.deps-changed }}"

          echo "Deploying version: $VERSION"

          # Create directory structure for setup installer
          mkdir -p "updates/setup/versions/$VERSION"

          # Copy setup files (from one.whispr-setup/.release-direct/)
          if [ -d "artifacts/one.whispr-setup/.release-direct" ]; then
            # Copy all files from .release-direct (includes squirrel-windows subdirectory)
            find artifacts/one.whispr-setup/.release-direct -name "*.exe" -exec cp {} "updates/setup/versions/$VERSION/" \;
            find artifacts/one.whispr-setup/.release-direct -name "*.nupkg" -exec cp {} "updates/setup/versions/$VERSION/" \;
            find artifacts/one.whispr-setup/.release-direct -name "RELEASES" -exec cp {} "updates/setup/versions/$VERSION/" \;
            echo "Direct distribution files copied to versions/$VERSION"
          fi

          # Create latest setup directory
          mkdir -p "updates/setup/latest"

          # Copy setup files to latest
          if [ -d "artifacts/one.whispr-setup/.release-direct" ]; then
            find artifacts/one.whispr-setup/.release-direct -name "*.exe" -exec cp {} "updates/setup/latest/" \;
            find artifacts/one.whispr-setup/.release-direct -name "*.nupkg" -exec cp {} "updates/setup/latest/" \;
            find artifacts/one.whispr-setup/.release-direct -name "RELEASES" -exec cp {} "updates/setup/latest/" \;
            echo "Direct distribution files copied to latest"
          fi

          # Handle different deployment scenarios based on what changed
          if [ "$DEPS_CHANGED" = "true" ]; then
            echo "Full rebuild - deploying base runtime and updatable bytecode"

            # Deploy compressed base runtime (~1.3GB)
            mkdir -p "updates/backend-runtime/versions/$VERSION"
            cp "artifacts/python-builds/one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" "updates/backend-runtime/versions/$VERSION/" 2>/dev/null || echo "No runtime base found"

            # Create latest runtime directory
            mkdir -p "updates/backend-runtime/latest"
            cp "artifacts/python-builds/one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" "updates/backend-runtime/latest/" 2>/dev/null || echo "No runtime base found"

            # Deploy scripts bytecode (~200KB)
            mkdir -p "updates/backend-scripts/versions/$VERSION"
            cp "artifacts/python-builds/one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/versions/$VERSION/" 2>/dev/null || echo "No scripts found"

            # Create latest scripts directory
            mkdir -p "updates/backend-scripts/latest"
            cp "artifacts/python-builds/one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/latest/" 2>/dev/null || echo "No scripts found"

            # Mark that both runtime and scripts were deployed
            touch "updates/RUNTIME_DEPLOYED"
            touch "updates/SCRIPTS_DEPLOYED"

          elif [ "$MAIN_WHISPR_CHANGED" = "true" ]; then
            echo "Quick update - deploying only bytecode changes"

            # Deploy updated scripts package (~200KB quick update)
            mkdir -p "updates/backend-scripts/versions/$VERSION"
            cp "artifacts/python-builds/one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/versions/$VERSION/" 2>/dev/null || echo "No scripts found"

            # Create latest scripts directory
            mkdir -p "updates/backend-scripts/latest"
            cp "artifacts/python-builds/one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" "updates/backend-scripts/latest/" 2>/dev/null || echo "No scripts found"

            # Mark that only scripts were deployed
            touch "updates/SCRIPTS_DEPLOYED"
          fi

          # Always deploy main app
          mkdir -p "updates/main-app/versions/$VERSION"
          cp -r artifacts/one.whispr-app/.release/win-unpacked/* "updates/main-app/versions/$VERSION/"

          mkdir -p "updates/main-app/latest"
          cp -r artifacts/one.whispr-app/.release/win-unpacked/* "updates/main-app/latest/"
        shell: bash

      - name: Create JSON manifest and transfer files to VPS
        run: |
          VERSION="${{ steps.get-params.outputs.version }}"

          # Create JSON manifest for the setup app to know what to download
          cd "updates/main-app/versions/$VERSION"

          # Get all files and create JSON manifest
          find . -type f -exec bash -c '
            file="$1"
            relativePath="${file#./}"
            size=$(stat -c%s "$file")
            checksum=$(sha256sum "$file" | cut -d" " -f1)
            baseUrl="https://whispr.one/updates/main-app/latest"

            echo "  {\"path\": \"./$relativePath\", \"size\": $size, \"checksum\": \"$checksum\", \"url\": \"$baseUrl/$relativePath\"}"
          ' _ {} \; > /tmp/files.json

          # Create manifest
          cat > "../../../manifest-$VERSION.json" << EOF
          {
            "version": "$VERSION",
            "files": [
          $(cat /tmp/files.json | sed '$!s/$/,/')
            ],
            "totalSize": $(find . -type f -exec stat -c%s {} \; | awk '{sum+=$1} END {print sum}')
          }
          EOF

          cd ../../../../
          cp "updates/manifest-$VERSION.json" "updates/main-app/latest/manifest.json"

          # Create updates directory on VPS (in nginx web root)
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p /var/www/html/updates"

          # Use scp for reliable transfer (more compatible than rsync)
          scp -r -o StrictHostKeyChecking=no updates/* ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:/var/www/html/updates/
        shell: bash

      - name: Update version info and cleanup old versions
        run: |
          ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'ENDSSH'
            cd /var/www/html/updates

            # Extract version from the uploaded files
            VERSION=$(ls setup/versions/ | sort -V | tail -1)

            # Create version.json for setup installer
            cat > setup/version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "downloadUrl": "https://whispr.one/updates/setup/latest/OneWhisprSetup.exe",
              "releaseNotes": "Latest version of OneWhispr Setup",
              "deltaUpdatesUrl": "https://whispr.one/updates/setup/versions/$VERSION/"
            }
            EOF

            # Create latest.yml for electron-updater
            SETUP_FILE_SIZE=$(stat -c%s setup/latest/OneWhisprSetup.exe)
            SETUP_SHA512=$(sha512sum setup/latest/OneWhisprSetup.exe | cut -d' ' -f1)
            cat > setup/latest.yml << EOF
            version: $VERSION
            files:
              - url: OneWhisprSetup.exe
                sha512: $SETUP_SHA512
                size: $SETUP_FILE_SIZE
            path: OneWhisprSetup.exe
            sha512: $SETUP_SHA512
            releaseDate: $(date -u +%Y-%m-%dT%H:%M:%SZ)
            EOF

            # Create version.json for main app updates
            cat > main-app/version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Latest version of One Whispr",
              "downloadUrl": "https://whispr.one/updates/main-app/latest/",
              "manifestUrl": "https://whispr.one/updates/main-app/latest/manifest.json",
              "versionsUrl": "https://whispr.one/updates/main-app/versions/$VERSION/"
            }
            EOF

            # Create version.json for backend runtime (only if deployed in this run)
            if [ -f "RUNTIME_DEPLOYED" ]; then
              echo "Creating backend runtime version file (runtime was deployed)..."
              RUNTIME_CHECKSUM=$(sha256sum backend-runtime/latest/OneWhispr-Runtime-Base.7z | cut -d' ' -f1)
              cat > backend-runtime/runtime-version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Backend runtime base package",
              "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
              "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$VERSION/",
              "compressionType": "7z-lzma2-ultra",
              "checksum": "$RUNTIME_CHECKSUM"
            }
            EOF
              rm -f "RUNTIME_DEPLOYED"
            fi

            # Create version.json for backend scripts (only if deployed in this run)
            if [ -f "SCRIPTS_DEPLOYED" ]; then
              echo "Creating backend scripts version file (scripts were deployed)..."
              SCRIPTS_CHECKSUM=$(sha256sum backend-scripts/latest/OneWhispr-Scripts.7z | cut -d' ' -f1)
              cat > backend-scripts/scripts-version.json << EOF
            {
              "version": "$VERSION",
              "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
              "releaseNotes": "Backend scripts bytecode update",
              "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
              "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$VERSION/",
              "updateType": "bytecode",
              "compressionType": "7z",
              "checksum": "$SCRIPTS_CHECKSUM"
            }
            EOF
              rm -f "SCRIPTS_DEPLOYED"
            fi

            # Keep only last 5 versions (cleanup old ones)
            cd setup/versions
            ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            cd ../../main-app/versions
            ls -1 | sort -V | head -n -5 | xargs -r rm -rf

            # Cleanup backend runtime versions if they exist
            if [ -d "../../backend-runtime/versions" ]; then
              cd ../../backend-runtime/versions
              ls -1 | sort -V | head -n -5 | xargs -r rm -rf
              cd ../../
            fi

            # Cleanup backend scripts versions if they exist
            if [ -d "backend-scripts/versions" ]; then
              cd backend-scripts/versions
              ls -1 | sort -V | head -n -5 | xargs -r rm -rf
              cd ../../
            fi

            echo "Deployment completed successfully!"
          ENDSSH
        shell: bash

  rollback:
    runs-on: ubuntu-latest
    if: inputs.rollback_to_version != '' || inputs.emergency_rollback == true

    steps:
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Execute rollback
        run: |
          if [ "${{ inputs.emergency_rollback }}" = "true" ]; then
            echo "Performing emergency rollback to previous version..."
            ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd ${{ secrets.DEPLOY_PATH }}/scripts && ./rollback.sh --emergency"
          else
            echo "Rolling back to version ${{ inputs.rollback_to_version }}..."
            ssh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd ${{ secrets.DEPLOY_PATH }}/scripts && ./rollback.sh --version ${{ inputs.rollback_to_version }}"
          fi
        shell: bash
